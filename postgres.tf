# AWS RDS PostgreSQL Database Infrastructure

resource "aws_iam_role" "rds_enhanced_monitoring" {
  name = "boostcast-rds-enhanced-monitoring"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "monitoring.rds.amazonaws.com"
        }
      }
    ]
  })

  tags = {
    Name    = "boostcast-rds-enhanced-monitoring"
    Project = "boostcast"
  }
}

resource "aws_iam_role_policy_attachment" "rds_enhanced_monitoring" {
  role       = aws_iam_role.rds_enhanced_monitoring.name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AmazonRDSEnhancedMonitoringRole"
}

# Public Subnets for RDS (AWS requires 2 for database subnet group)
resource "aws_subnet" "rds_public_subnet_a" {
  vpc_id            = aws_vpc.swarm_vpc.id
  cidr_block        = "********/24"
  availability_zone = "${var.aws_region}a"
  map_public_ip_on_launch = true

  tags = {
    Name = "boostcast-rds-public-subnet-a"
    Project = "boostcast"
  }
}

resource "aws_subnet" "rds_public_subnet_b" {
  vpc_id            = aws_vpc.swarm_vpc.id
  cidr_block        = "********/24"
  availability_zone = "${var.aws_region}b"
  map_public_ip_on_launch = true

  tags = {
    Name = "boostcast-rds-public-subnet-b"
    Project = "boostcast"
  }
}

# Route table for public subnets (with internet gateway)
resource "aws_route_table" "rds_public_rt" {
  vpc_id = aws_vpc.swarm_vpc.id

  route {
    cidr_block = "0.0.0.0/0"
    gateway_id = aws_internet_gateway.swarm_igw.id
  }

  tags = {
    Name = "boostcast-rds-public-rt"
    Project = "boostcast"
  }
}

# Route table associations for public subnets
resource "aws_route_table_association" "rds_public_rta_a" {
  subnet_id      = aws_subnet.rds_public_subnet_a.id
  route_table_id = aws_route_table.rds_public_rt.id
}

resource "aws_route_table_association" "rds_public_rta_b" {
  subnet_id      = aws_subnet.rds_public_subnet_b.id
  route_table_id = aws_route_table.rds_public_rt.id
}

# Database Subnet Group
resource "aws_db_subnet_group" "postgres_subnet_group" {
  name       = "boostcast-postgres-subnet-group"
  subnet_ids = [aws_subnet.rds_public_subnet_a.id, aws_subnet.rds_public_subnet_b.id]

  tags = {
    Name = "boostcast-postgres-subnet-group"
    Project = "boostcast"
  }
}

# Security Group for RDS PostgreSQL
resource "aws_security_group" "rds_postgres_sg" {
  name        = "boostcast-rds-postgres-sg"
  description = "Security group for RDS PostgreSQL database"
  vpc_id      = aws_vpc.swarm_vpc.id

  # PostgreSQL access from swarm cluster
  ingress {
    from_port       = 5432
    to_port         = 5432
    protocol        = "tcp"
    security_groups = [aws_security_group.swarm_master_sg.id]
  }

  # PostgreSQL access from internet (WARNING: Security Risk!)
  ingress {
    from_port   = 5432
    to_port     = 5432
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  egress {
  from_port   = 0
  to_port     = 0
  protocol    = "-1"
  cidr_blocks = ["0.0.0.0/0"]
}
  tags = {
    Name = "boostcast-rds-postgres-sg"
    Project = "boostcast"
  }
}

# Random password for database master user
resource "random_password" "postgres_master_password" {
  length  = 16
  special = true
  override_special = "!@#$%&*()-_=+[]{}<>:?"
}

# RDS PostgreSQL Instance
resource "aws_db_instance" "postgres" {
  identifier = "boostcast-postgres"

  # Engine configuration
  engine         = "postgres"
  engine_version = "16"
  instance_class = "db.t4g.small"

  # Storage configuration
  allocated_storage     = 20
  max_allocated_storage = 100
  storage_type          = "gp3"
  storage_encrypted     = true

  # Database configuration
  db_name  = "boostcast_api"
  username = "postgres"
  password = random_password.postgres_master_password.result

  # Network configuration
  db_subnet_group_name   = aws_db_subnet_group.postgres_subnet_group.name
  vpc_security_group_ids = [aws_security_group.rds_postgres_sg.id]
  publicly_accessible    = true

  # Backup configuration
  backup_retention_period = 7
  backup_window          = "03:00-04:00"
  maintenance_window     = "sun:04:00-sun:05:00"

   #Enable basic monitoring (5-minute intervals are free)
  monitoring_interval = 60
  monitoring_role_arn = aws_iam_role.rds_enhanced_monitoring.arn

  # Enable Performance Insights for production workloads
  performance_insights_enabled = true
  performance_insights_retention_period = 7  # Free tier

  # Deletion protection
  deletion_protection = true
  skip_final_snapshot = false

  tags = {
    Name = "boostcast-postgres"
    Project = "boostcast"
  }
}
