# AWS Simple Email Service (SES) Configuration for BoostCast

# SES Domain Identity for boostcast.pro
resource "aws_ses_domain_identity" "boostcast_domain" {
  domain = aws_route53_zone.boostcast_domain.name
}

# SES Domain DKIM Configuration
resource "aws_ses_domain_dkim" "boostcast_domain_dkim" {
  domain = aws_ses_domain_identity.boostcast_domain.domain
}

# Route53 TXT record for SES domain verification
resource "aws_route53_record" "ses_domain_verification" {
  zone_id = aws_route53_zone.boostcast_domain.zone_id
  name    = "_amazonses.${aws_ses_domain_identity.boostcast_domain.domain}"
  type    = "TXT"
  ttl     = 300
  records = [aws_ses_domain_identity.boostcast_domain.verification_token]
}

# Route53 CNAME records for DKIM verification (3 records)
resource "aws_route53_record" "ses_dkim_records" {
  count   = 3
  zone_id = aws_route53_zone.boostcast_domain.zone_id
  name    = "${element(aws_ses_domain_dkim.boostcast_domain_dkim.dkim_tokens, count.index)}._domainkey"
  type    = "CNAME"
  ttl     = 300
  records = ["${element(aws_ses_domain_dkim.boostcast_domain_dkim.dkim_tokens, count.index)}.dkim.amazonses.com"]
}

# IAM User for SES SMTP credentials
resource "aws_iam_user" "ses_smtp_user" {
  name = "boostcast-ses-smtp-user"
  path = "/"

  tags = {
    Name    = "boostcast-ses-smtp-user"
    Project = "boostcast"
    Purpose = "SES SMTP Authentication"
  }
}

# IAM Policy for SES sending permissions
resource "aws_iam_policy" "ses_sending_policy" {
  name        = "boostcast-ses-sending-policy"
  description = "Policy for sending emails via SES"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "ses:SendEmail",
          "ses:SendRawEmail"
        ]
        Resource = "*"
        Condition = {
          StringLike = {
            "ses:FromAddress" = [
              "*@${aws_ses_domain_identity.boostcast_domain.domain}"
              ]
          }
        }
      }
    ]
  })

  tags = {
    Name    = "boostcast-ses-sending-policy"
    Project = "boostcast"
  }
}

# Attach the SES policy to the IAM user
resource "aws_iam_user_policy_attachment" "ses_smtp_user_policy" {
  user       = aws_iam_user.ses_smtp_user.name
  policy_arn = aws_iam_policy.ses_sending_policy.arn
}

# IAM Access Key for SMTP credentials
resource "aws_iam_access_key" "ses_smtp_credentials" {
  user = aws_iam_user.ses_smtp_user.name

  # Lifecycle management to prevent accidental deletion
  lifecycle {
    create_before_destroy = true
  }
}

# SES Domain Identity Verification (waits for verification to complete)
resource "aws_ses_domain_identity_verification" "boostcast_domain_verification" {
  domain = aws_ses_domain_identity.boostcast_domain.id

  depends_on = [aws_route53_record.ses_domain_verification]

  timeouts {
    create = "5m"
  }
}

# Custom MAIL FROM domain for better deliverability
resource "aws_ses_domain_mail_from" "boostcast_mail_from" {
  domain           = aws_ses_domain_identity.boostcast_domain.domain
  mail_from_domain = "mail.${aws_ses_domain_identity.boostcast_domain.domain}"
}

# MX record for MAIL FROM domain
resource "aws_route53_record" "ses_mail_from_mx" {
  zone_id = aws_route53_zone.boostcast_domain.zone_id
  name    = "mail.${aws_ses_domain_identity.boostcast_domain.domain}"
  type    = "MX"
  ttl     = 300
  records = ["10 feedback-smtp.${var.aws_region}.amazonses.com"]
}

# SPF record for MAIL FROM domain
resource "aws_route53_record" "ses_mail_from_spf" {
  zone_id = aws_route53_zone.boostcast_domain.zone_id
  name    = "mail.${aws_ses_domain_identity.boostcast_domain.domain}"
  type    = "TXT"
  ttl     = 300
  records = ["v=spf1 include:amazonses.com ~all"]
}



# SES Virtual Delivery Manager (VDM) Configuration
resource "aws_sesv2_account_vdm_attributes" "boostcast_vdm" {
  vdm_enabled = "ENABLED"

  dashboard_attributes {
    engagement_metrics = "ENABLED"
  }

  guardian_attributes {
    optimized_shared_delivery = "ENABLED"
  }
}


# SES Configuration Set with VDM and advanced features
resource "aws_sesv2_configuration_set" "boostcast_config_set" {
  configuration_set_name = "boostcast-email-config"

  delivery_options {
    tls_policy = "REQUIRE"
  }

  reputation_options {
    reputation_metrics_enabled = true
  }

  sending_options {
    sending_enabled = true
  }

  suppression_options {
    suppressed_reasons = ["BOUNCE", "COMPLAINT"]
  }

  tracking_options {
    custom_redirect_domain = aws_route53_zone.boostcast_domain.name
  }

  vdm_options {
    dashboard_options {
      engagement_metrics = "ENABLED"
    }
    guardian_options {
      optimized_shared_delivery = "ENABLED"
    }
  }

  tags = {
    Name    = "boostcast-email-config"
    Project = "boostcast"
    Purpose = "VDM-enabled configuration set"
  }

  # Ensure domain is verified before creating configuration set
  depends_on = [aws_ses_domain_identity_verification.boostcast_domain_verification]
}

# Add CloudWatch event destination
resource "aws_sesv2_configuration_set_event_destination" "cloudwatch" {
  configuration_set_name = aws_sesv2_configuration_set.boostcast_config_set.configuration_set_name
  event_destination_name = "cloudwatch-destination"

  event_destination {
    cloud_watch_destination {
      dimension_configuration {
        default_dimension_value = "default"
        dimension_name         = "MessageTag"
        dimension_value_source = "MESSAGE_TAG"
      }
    }

    enabled              = true
    matching_event_types = ["BOUNCE", "COMPLAINT", "DELIVERY", "REJECT", "SEND"]
  }
}

# CNAME records for custom click/open tracking
resource "aws_route53_record" "ses_tracking" {
  for_each = toset(["click", "open"])

  zone_id = aws_route53_zone.boostcast_domain.zone_id
  name    = each.key
  type    = "CNAME"
  ttl     = 300
  records = ["r.${var.aws_region}.awstrack.me"]
}
