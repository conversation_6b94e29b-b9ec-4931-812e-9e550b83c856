# terraform {
#   required_providers {
#     digitalocean = {
#       source = "digitalocean/digitalocean"
#       version = "2.50.0"
#     }
#   }
# }
#
# variable "do_token" {}
# variable "do_access_id" {}
# variable "do_secret_key" {}
#
# # Configure the DigitalOcean Provider
# provider "digitalocean" {
#   token = var.do_token
#   spaces_access_id  = var.do_access_id
#   spaces_secret_key = var.do_secret_key
#
# }

terraform {
  required_providers {
    aws = {
      source = "hashicorp/aws"
      version = "6.0.0-beta3"
    }
    random = {
      source = "hashicorp/random"
      version = "~> 3.1"
    }
  }
}

provider "aws" {
  region     = var.aws_region
  access_key = var.aws_access_key
  secret_key = var.aws_secret_key
}

# Provider for us-east-1 (required for CloudFront ACM certificates)
provider "aws" {
  alias      = "us_east_1"
  region     = "us-east-1"
  access_key = var.aws_access_key
  secret_key = var.aws_secret_key
}