stages:
  - validate-and-plan
  - apply
image:
  name: hashicorp/terraform:light
  entrypoint:
    - '/usr/bin/env'
    - 'PATH=/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin'
before_script:
  - rm -rf .terraform
  - terraform --version
  - terraform init
validate-and-plan:
  stage: validate-and-plan
  script:
    - terraform validate
    - terraform plan
apply:
  stage: apply
  script:
    - terraform apply -input=false -auto-approve
    - terraform output -json 2>/dev/null > terraform-output.json
  dependencies:
    - validate-and-plan
  artifacts:
    paths:
      - terraform-output.json
  when: manual