# resource "digitalocean_ssh_key" "ssh_key" {
#   name = "ssh_key"
#   public_key = var.cicd_pub_key
# }
#
# resource "null_resource" "should_recreate_api" {
#   triggers = {
#     dummy_value = "123"
#   }
# }
#
# resource "digitalocean_droplet" "master_cluster_node" {
#  name   = "master-cluster-node-1"
#  size   = "c-4-intel"
#  image  = "docker-20-04"
#  region = var.deployment_zone
#  ssh_keys = [digitalocean_ssh_key.ssh_key.fingerprint]
#  lifecycle {
#    replace_triggered_by = [null_resource.should_recreate_api]
#  }
# }
