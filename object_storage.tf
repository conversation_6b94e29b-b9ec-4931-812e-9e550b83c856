# AWS S3 Buckets for BoostCast

# Source Videos Bucket - Private bucket for storing source video files
resource "aws_s3_bucket" "source_videos" {
  bucket = "boostcast-source-videos"
}

resource "aws_s3_bucket_public_access_block" "source_videos" {
  bucket = aws_s3_bucket.source_videos.id

  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}

resource "aws_s3_bucket_cors_configuration" "source_videos" {
  bucket = aws_s3_bucket.source_videos.id

  cors_rule {
    allowed_headers = ["*"]
    allowed_methods = ["GET", "PUT", "POST", "DELETE", "HEAD"]
    allowed_origins = ["*"]
    expose_headers  = ["ETag", "x-amz-meta-custom-header"]
    max_age_seconds = 3000
  }
}

# Clips Bucket - Private bucket for storing video clips (no CDN)
resource "aws_s3_bucket" "clips" {
  bucket = "boostcast-clips"
}

resource "aws_s3_bucket_public_access_block" "clips" {
  bucket = aws_s3_bucket.clips.id

  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}

# Video Thumbnails Bucket - Private bucket for storing video thumbnails
resource "aws_s3_bucket" "video_thumbnails" {
  bucket = "boostcast-video-thumbnails"
}

resource "aws_s3_bucket_public_access_block" "video_thumbnails" {
  bucket = aws_s3_bucket.video_thumbnails.id

  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}

# Temp Audio Bucket - Private bucket for temporary audio files
resource "aws_s3_bucket" "temp_audio" {
  bucket = "boostcast-temp-audio"
}

resource "aws_s3_bucket_public_access_block" "temp_audio" {
  bucket = aws_s3_bucket.temp_audio.id

  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}
# Fonts Bucket - Private bucket for storing fonts
resource "aws_s3_bucket" "fonts" {
  bucket = "boostcast-fonts"
}

resource "aws_s3_bucket_public_access_block" "fonts" {
  bucket = aws_s3_bucket.fonts.id

  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}

resource "aws_s3_bucket_cors_configuration" "fonts" {
  bucket = aws_s3_bucket.fonts.id

  cors_rule {
    allowed_headers = ["*"]
    allowed_methods = ["GET", "PUT", "POST", "DELETE", "HEAD"]
    allowed_origins = ["*"]
    expose_headers  = ["ETag", "x-amz-meta-custom-header"]
    max_age_seconds = 3000
  }
}

# Assets Bucket - Public bucket for storing public assets
resource "aws_s3_bucket" "assets" {
  bucket = "boostcast-assets"
}

# Allow public access for assets bucket (required for public read policy)
resource "aws_s3_bucket_public_access_block" "assets" {
  bucket = aws_s3_bucket.assets.id

  block_public_acls       = false
  block_public_policy     = false
  ignore_public_acls      = false
  restrict_public_buckets = false
}

resource "aws_s3_bucket_cors_configuration" "assets" {
  bucket = aws_s3_bucket.assets.id

  cors_rule {
    allowed_headers = ["*"]
    allowed_methods = ["GET", "PUT", "POST", "DELETE", "HEAD"]
    allowed_origins = ["*"]
    expose_headers  = ["ETag", "x-amz-meta-custom-header"]
    max_age_seconds = 3000
  }
}

# Public read policy for assets bucket
resource "aws_s3_bucket_policy" "assets_public_read" {
  bucket = aws_s3_bucket.assets.id
  depends_on = [aws_s3_bucket_public_access_block.assets]

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Sid       = "PublicReadGetObject"
        Effect    = "Allow"
        Principal = "*"
        Action    = "s3:GetObject"
        Resource  = "${aws_s3_bucket.assets.arn}/*"
      }
    ]
  })
}

# ACM Certificate for CloudFront (must be in us-east-1)
resource "aws_acm_certificate" "assets_cert" {
  provider          = aws.us_east_1
  domain_name       = "boostcast-assets.boostcast.pro"
  validation_method = "DNS"

  lifecycle {
    create_before_destroy = true
  }

  tags = {
    Name    = "boostcast-assets-cert"
    Project = "boostcast"
  }
}



# Route53 records for certificate validation
resource "aws_route53_record" "assets_cert_validation" {
  for_each = {
    for dvo in aws_acm_certificate.assets_cert.domain_validation_options : dvo.domain_name => {
      name   = dvo.resource_record_name
      record = dvo.resource_record_value
      type   = dvo.resource_record_type
    }
  }

  allow_overwrite = true
  name            = each.value.name
  records         = [each.value.record]
  ttl             = 60
  type            = each.value.type
  zone_id         = aws_route53_zone.boostcast_domain.zone_id
}



# Certificate validation
resource "aws_acm_certificate_validation" "assets_cert" {
  provider                = aws.us_east_1
  certificate_arn         = aws_acm_certificate.assets_cert.arn
  validation_record_fqdns = [for record in aws_route53_record.assets_cert_validation : record.fqdn]
}



# CloudFront Response Headers Policy for Security
resource "aws_cloudfront_response_headers_policy" "security_headers" {
  name    = "boostcast-assets-security-headers"
  comment = "Security headers policy for BoostCast assets"

  security_headers_config {
    strict_transport_security {
      access_control_max_age_sec = 31536000
      include_subdomains         = true
      preload                    = true
      override                   = true
    }

    content_type_options {
      override = true
    }

    frame_options {
      frame_option = "DENY"
      override     = true
    }

    referrer_policy {
      referrer_policy = "strict-origin-when-cross-origin"
      override        = true
    }
  }

  cors_config {
    access_control_allow_credentials = false
    access_control_allow_headers {
      items = ["*"]
    }
    access_control_allow_methods {
      items = ["GET", "HEAD", "OPTIONS"]
    }
    access_control_allow_origins {
      items = ["*"]
    }
    access_control_max_age_sec = 86400
    origin_override            = true
  }
}

# CloudFront distribution for assets bucket
resource "aws_cloudfront_distribution" "assets_distribution" {
  origin {
    domain_name = aws_s3_bucket.assets.bucket_regional_domain_name
    origin_id   = "S3-${aws_s3_bucket.assets.bucket}"
  }

  enabled             = true
  is_ipv6_enabled     = true
  comment             = "CloudFront distribution for BoostCast assets"
  default_root_object = "index.html"

  aliases = ["boostcast-assets.boostcast.pro"]

  default_cache_behavior {
    allowed_methods  = ["GET", "HEAD", "OPTIONS"]
    cached_methods   = ["GET", "HEAD"]
    target_origin_id = "S3-${aws_s3_bucket.assets.bucket}"

    forwarded_values {
      query_string = false
      cookies {
        forward = "none"
      }
    }

    viewer_protocol_policy = "redirect-to-https"
    min_ttl                = 0
    default_ttl            = 3600
    max_ttl                = 86400

    response_headers_policy_id = aws_cloudfront_response_headers_policy.security_headers.id
  }

  price_class = "PriceClass_100"

  restrictions {
    geo_restriction {
      restriction_type = "none"
    }
  }

  viewer_certificate {
    acm_certificate_arn = aws_acm_certificate_validation.assets_cert.certificate_arn
    ssl_support_method  = "sni-only"
  }

  tags = {
    Name    = "boostcast-assets-distribution"
    Project = "boostcast"
  }
}

# Route53 CNAME record for custom domain
resource "aws_route53_record" "assets_cname" {
  zone_id = aws_route53_zone.boostcast_domain.zone_id
  name    = "boostcast-assets"
  type    = "CNAME"
  ttl     = 300
  records = [aws_cloudfront_distribution.assets_distribution.domain_name]
}
