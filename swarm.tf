# AWS VPC and Networking for Swarm Cluster
resource "aws_vpc" "swarm_vpc" {
  cidr_block           = "10.0.0.0/16"
  enable_dns_hostnames = true
  enable_dns_support   = true

  tags = {
    Name = "boostcast-swarm-vpc"
    Project = "boostcast"
  }
}

# Internet Gateway
resource "aws_internet_gateway" "swarm_igw" {
  vpc_id = aws_vpc.swarm_vpc.id

  tags = {
    Name = "boostcast-swarm-igw"
    Project = "boostcast"
  }
}

# Public Subnet
resource "aws_subnet" "swarm_public_subnet" {
  vpc_id                  = aws_vpc.swarm_vpc.id
  cidr_block              = "********/24"
  availability_zone       = "${var.aws_region}a"
  map_public_ip_on_launch = true

  tags = {
    Name = "boostcast-swarm-public-subnet"
    Project = "boostcast"
  }
}

# Route Table for Public Subnet
resource "aws_route_table" "swarm_public_rt" {
  vpc_id = aws_vpc.swarm_vpc.id

  route {
    cidr_block = "0.0.0.0/0"
    gateway_id = aws_internet_gateway.swarm_igw.id
  }

  tags = {
    Name = "boostcast-swarm-public-rt"
    Project = "boostcast"
  }
}

# Route Table Association
resource "aws_route_table_association" "swarm_public_rta" {
  subnet_id      = aws_subnet.swarm_public_subnet.id
  route_table_id = aws_route_table.swarm_public_rt.id
}

# Security Group for Swarm Master Node
resource "aws_security_group" "swarm_master_sg" {
  name        = "boostcast-swarm-master-sg"
  description = "Security group for Swarm master cluster node"
  vpc_id      = aws_vpc.swarm_vpc.id

  # SSH access
  ingress {
    from_port   = 22
    to_port     = 22
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  # Docker Swarm management port
  ingress {
    from_port   = 2377
    to_port     = 2377
    protocol    = "tcp"
    cidr_blocks = ["10.0.0.0/16"]
  }

  # Docker Swarm communication port
  ingress {
    from_port   = 7946
    to_port     = 7946
    protocol    = "tcp"
    cidr_blocks = ["10.0.0.0/16"]
  }

  ingress {
    from_port   = 7946
    to_port     = 7946
    protocol    = "udp"
    cidr_blocks = ["10.0.0.0/16"]
  }

  # Docker Swarm overlay network port
  ingress {
    from_port   = 4789
    to_port     = 4789
    protocol    = "udp"
    cidr_blocks = ["10.0.0.0/16"]
  }

  # HTTP
  ingress {
    from_port   = 80
    to_port     = 80
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  # HTTPS
  ingress {
    from_port   = 443
    to_port     = 443
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  # All outbound traffic
  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = {
    Name = "boostcast-swarm-master-sg"
    Project = "boostcast"
  }
}

# Key Pair for SSH access (you'll need to provide the public key)
resource "aws_key_pair" "swarm_key" {
  key_name   = "boostcast-swarm-key"
  public_key = var.cicd_pub_key

  tags = {
    Name = "boostcast-swarm-key"
    Project = "boostcast"
  }
}

# Get the latest Ubuntu 22.04 LTS AMI for ARM64
data "aws_ami" "ubuntu_arm64" {
  most_recent = true
  owners      = ["099720109477"] # Canonical

  filter {
    name   = "name"
    values = ["ubuntu/images/hvm-ssd/ubuntu-jammy-22.04-arm64-server-*"]
  }

  filter {
    name   = "virtualization-type"
    values = ["hvm"]
  }
}

# Swarm Master Cluster Node
resource "aws_instance" "master_cluster_node" {
  ami                    = data.aws_ami.ubuntu_arm64.id
  instance_type          = "m6g.xlarge"
  key_name              = aws_key_pair.swarm_key.key_name
  vpc_security_group_ids = [aws_security_group.swarm_master_sg.id]
  subnet_id             = aws_subnet.swarm_public_subnet.id

  lifecycle {
    ignore_changes = [public_ip, public_dns, ami]  # Ignore false positives from Terraform when public IP changes
  }

  root_block_device {
    volume_type = "gp3"
    volume_size = 100
    encrypted   = true
    
    tags = {
      Name = "boostcast-swarm-master-root-volume"
      Project = "boostcast"
    }
  }

  user_data_base64 = base64encode(<<-EOF
    #!/bin/bash
    set -e

    # Update system
    apt-get update
    apt-get upgrade -y

    # Install Docker CE (specific version for consistency)
    apt-get install -y ca-certificates curl gnupg lsb-release

    # Add Docker's official GPG key
    mkdir -p /etc/apt/keyrings
    curl -fsSL https://download.docker.com/linux/ubuntu/gpg | gpg --dearmor -o /etc/apt/keyrings/docker.gpg

    # Set up the repository
    echo \
      "deb [arch=$(dpkg --print-architecture) signed-by=/etc/apt/keyrings/docker.gpg] https://download.docker.com/linux/ubuntu \
      $(lsb_release -cs) stable" | tee /etc/apt/sources.list.d/docker.list > /dev/null

    # Install Docker Engine
    apt-get update
    apt-get install -y docker-ce docker-ce-cli containerd.io docker-compose-plugin

    # Start and enable Docker
    systemctl start docker
    systemctl enable docker

    # Add ubuntu user to docker group
    usermod -aG docker ubuntu

    # Initialize Docker Swarm
    PRIVATE_IP=$(curl -s http://***************/latest/meta-data/local-ipv4)
    docker swarm init --advertise-addr $PRIVATE_IP

    # Install additional tools
    apt-get install -y htop curl wget git jq

    # Create directory structure
    mkdir -p /opt/swarm
    chown ubuntu:ubuntu /opt/swarm

    # Log completion
    echo "Swarm master initialization completed at $(date)" >> /var/log/swarm-init.log
  EOF
  )

  tags = {
    Name = "boostcast-swarm-master"
    Project = "boostcast"
    Role = "swarm-master"
  }
}

# Elastic IP for consistent public IP
resource "aws_eip" "swarm_master_eip" {
  domain = "vpc"

  tags = {
    Name    = "boostcast-swarm-master-eip"
    Project = "boostcast"
  }
}

# Elastic IP Association
resource "aws_eip_association" "swarm_master_eip_assoc" {
  instance_id   = aws_instance.master_cluster_node.id
  allocation_id = aws_eip.swarm_master_eip.id
}
