output "POSTGRES_URL" {
  value = "postgresql://${aws_db_instance.postgres.username}:${random_password.postgres_master_password.result}@${aws_db_instance.postgres.endpoint}/${aws_db_instance.postgres.db_name}"
  sensitive = true
}

# S3 Bucket Names (for application configuration)
output "S3_SOURCE_VIDEOS_BUCKET" {
  value = aws_s3_bucket.source_videos.bucket
}

output "S3_CLIPS_BUCKET" {
  value = aws_s3_bucket.clips.bucket
}

output "S3_VIDEO_THUMBNAILS_BUCKET" {
  value = aws_s3_bucket.video_thumbnails.bucket
}

output "S3_TEMP_AUDIO_BUCKET" {
  value = aws_s3_bucket.temp_audio.bucket
}

output "S3_FONTS_BUCKET" {
  value = aws_s3_bucket.fonts.bucket
}

# Public assets bucket URL via CloudFront custom domain
output "S3_ASSETS_URL" {
  value = "https://${aws_route53_record.assets_cname.fqdn}"
  description = "Public URL for Boostcast assets via CloudFront"
}

output "S3_ASSETS_BUCKET" {
  value = aws_s3_bucket.assets.bucket
}

output "SWARM_MASTER_PUBLIC_IP" {
  value       = aws_eip.swarm_master_eip.public_ip
  description = "Public IP address of the Swarm master node"
}

output "API_BASE_URL" {
  value       = "https://${aws_route53_record.api_a_record.fqdn}"
  description = "Base URL for the API subdomain"
}

output "CLUSTER_MANAGER_URL" {
  value       = "https://${aws_route53_record.cluster_manager_a_record.fqdn}"
  description = "URL for the cluster manager subdomain"
}

# SES SMTP Configuration Outputs
output "MAIL_SERVER" {
  value       = "email-smtp.${var.aws_region}.amazonaws.com"
  description = "AWS SES SMTP server address"
}

output "MAIL_PORT" {
  value       = 587
  description = "AWS SES SMTP port (TLS)"
}

output "MAIL_USERNAME" {
  value       = aws_iam_access_key.ses_smtp_credentials.id
  description = "AWS SES SMTP username (IAM Access Key ID)"
}

output "MAIL_PASSWORD" {
  value       =  aws_iam_access_key.ses_smtp_credentials.ses_smtp_password_v4
  description = "AWS SES SMTP password (converted IAM Secret)"
  sensitive = true
}

output "AWS_REGION" {
  value = var.aws_region
  description = "AWS Deployment region"
}

output "APP_BASE_URL" {
  value       = "https://create.${aws_route53_zone.boostcast_domain.name}"
  description = "Base URL for the Boostcast React application"
}


# Remotion Lambda Outputs (following official naming conventions)
output "REMOTION_AWS_ACCESS_KEY_ID" {
  value = aws_iam_access_key.remotion_user_key.id
  description = "AWS Access Key ID for Remotion Lambda CLI (use in .env file)"
}

output "REMOTION_AWS_SECRET_ACCESS_KEY" {
  value = aws_iam_access_key.remotion_user_key.secret
  sensitive = true
  description = "AWS Secret Access Key for Remotion Lambda CLI (use in .env file)"
}
