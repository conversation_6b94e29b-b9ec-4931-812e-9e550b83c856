# AWS Amplify Configuration for BoostCast Website

# Amplify App for the BoostCast website
resource "aws_amplify_app" "boostcast_website" {
  name       = "boostcast-website"
  repository = "https://gitlab.com/boostcast/boostcast-website"

  # GitLab access token for repository access
  access_token = var.amplify_gitlab_token

  # Platform settings for static site
  platform = "WEB"


  # Enable auto branch creation and deployment
  enable_auto_branch_creation = false
  enable_branch_auto_build    = true
  enable_branch_auto_deletion = false

  # Custom rules for Next.js static export routing
  custom_rule {
    source = "/<*>"
    status = "404-200"
    target = "/index.html"
  }

  # Environment variables for the Next.js website
  environment_variables = {
    NEXT_PUBLIC_ANALYTICS_ID     = var.website_google_analytics_id
    NEXT_PUBLIC_TAG_MANAGER_ID   = var.website_google_tag_manager_id
    NEXT_PUBLIC_HOTJAR_ID        = var.website_hotjar_id
    NEXT_PUBLIC_CLARITY_ID       = var.website_clarity_id
  }

  tags = {
    Name    = "boostcast-website"
    Project = "boostcast"
    Purpose = "Static website hosting"
  }
}

# Main branch configuration
resource "aws_amplify_branch" "main" {
  app_id      = aws_amplify_app.boostcast_website.id
  branch_name = "main"

  # Enable automatic builds on push
  enable_auto_build = true

  # Framework detection
  framework = "Next.js - SSG"

  # Stage for this branch
  stage = "PRODUCTION"

  tags = {
    Name    = "boostcast-website-main"
    Project = "boostcast"
    Branch  = "main"
  }
}

# Custom domain configuration for boostcast.pro
resource "aws_amplify_domain_association" "boostcast_domain" {
  app_id      = aws_amplify_app.boostcast_website.id
  domain_name = aws_route53_zone.boostcast_domain.name

  # Wait for certificate validation
  wait_for_verification = true

  # Subdomain configuration
  sub_domain {
    branch_name = aws_amplify_branch.main.branch_name
    prefix      = ""
  }

  sub_domain {
    branch_name = aws_amplify_branch.main.branch_name
    prefix      = "www"
  }

  depends_on = [aws_amplify_branch.main]
}
