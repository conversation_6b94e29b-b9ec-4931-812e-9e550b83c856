variable "app_google_analytics_id" {}

variable "app_hotjar_id" {}

variable "app_clarity_id" {}

variable "app_google_tag_manager_id" {}

variable "app_facebook_pixel_id" {}

variable "website_google_analytics_id" {}

variable "website_hotjar_id" {}

variable "website_clarity_id" {}

variable "website_google_tag_manager_id" {}

variable "website_facebook_pixel_id" {}

variable "turnstile_site_key" {}

variable "stripe_pricing_table_id" {}

variable "stripe_publishable_key" {}

variable "aws_region" {
	type = string
	default = "eu-west-1"
}

variable "aws_access_key" {}

variable "aws_secret_key" {}

variable "cicd_pub_key" {
  description = "SSH public key for accessing the swarm cluster nodes"
  type        = string
}

# Database configuration variables
variable "postgres_master_username" {
  description = "Master username for PostgreSQL database"
  type        = string
  default     = "postgres"
}

variable "postgres_database_name" {
  description = "Name of the PostgreSQL database"
  type        = string
  default     = "boostcast_main"
}

variable "amplify_gitlab_token" {
  description = "Gitlab access token for amplify deployments"
  type = string
}