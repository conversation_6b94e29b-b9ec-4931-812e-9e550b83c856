
# resource "digitalocean_record" "api_a_record" {
#  domain = digitalocean_domain.vidfast_domain.id
#  name = "api"
#  type = "A"
#  value = digitalocean_droplet.master_cluster_node.ipv4_address
# }
#
# resource "digitalocean_record" "cluster_manager_a_record" {
#   domain = digitalocean_domain.vidfast_domain.id
#   name   = "cluster-manager"
#   type = "A"
#   value = digitalocean_droplet.master_cluster_node.ipv4_address
# }
#
# resource "digitalocean_record" "app_cname_record" {
#   domain = digitalocean_domain.vidfast_domain.id
#   name   = "app"
#   type   = "CNAME"
#   value  = "${replace(digitalocean_app.vidfast-app.default_ingress, "https://", "")}."
#   ttl    = 300  # 5 minutes
#
# }
# resource "digitalocean_record" "wip_app_cname_record" {
#   domain = digitalocean_domain.vidfast_domain.id
#   name   = "w1p-app"
#   type   = "CNAME"
#   value  = "${replace(digitalocean_app.vidfast-wip-app.default_ingress, "https://", "")}."
#   ttl    = 300  # 5 minutes
# }
#
#
# resource "digitalocean_record" "website_a_www_record" {
#     domain = digitalocean_domain.vidfast_domain.id
#     name   = "www"
#     type   = "A"
#     value  = digitalocean_droplet.wordpress_website.ipv4_address
# }
#
# resource "digitalocean_record" "website_a_record" {
#     domain = digitalocean_domain.vidfast_domain.id
#     name   = "@"
#     type   = "A"
#     value  = digitalocean_droplet.wordpress_website.ipv4_address
# }
#
# resource "digitalocean_record" "email_mx_record" {
#     domain = digitalocean_domain.vidfast_domain.id
#     name   = "@"
#     type   = "MX"
#     value  = "smtp.google.com."
#     priority = 1
#     ttl = 3600
# }
#
# resource "digitalocean_record" "email_verification_mx_record" {
#     domain = digitalocean_domain.vidfast_domain.id
#     name   = "@"
#     type   = "MX"
#     value  = "rtb2ydhvdnjyead3m62a377nap37ycc6v4af77sc3lqqejcrlula.mx-verification.google.com."
#     ttl = 3600
#     priority = 15
# }
#
# resource "digitalocean_record" "spf_record" {
#   domain = digitalocean_domain.vidfast_domain.id
#   name   = "@"
#   type   = "TXT"
#   value  = "v=spf1 include:_spf.google.com ~all"
#   ttl    = 3600
# }
#
# resource "digitalocean_record" "google_search_console_record" {
#   domain = digitalocean_domain.vidfast_domain.id
#   name   = "@"
#   type   = "TXT"
#   value  = "google-site-verification=jMOsDPUbU4IAe2e0Df_tA_f8CF6vAF_-QtrhAiRRXRY"
# }
#
# resource "digitalocean_record" "sendgrid_url" {
#   domain = digitalocean_domain.vidfast_domain.id
#   type   = "CNAME"
#   name   = "url2145"
#   value  = "sendgrid.net."
# }
#
# resource "digitalocean_record" "sendgrid_45574143" {
#   domain = digitalocean_domain.vidfast_domain.id
#   type   = "CNAME"
#   name   = "45574143"
#   value  = "sendgrid.net."
# }
#
# resource "digitalocean_record" "sendgrid_em3912" {
#   domain = digitalocean_domain.vidfast_domain.id
#   type   = "CNAME"
#   name   = "em3912"
#   value  = "u45574143.wl144.sendgrid.net."
# }
#
# resource "digitalocean_record" "sendgrid_s1_domainkey" {
#   domain = digitalocean_domain.vidfast_domain.id
#   type   = "CNAME"
#   name   = "s1._domainkey"
#   value  = "s1.domainkey.u45574143.wl144.sendgrid.net."
# }
#
# resource "digitalocean_record" "sendgrid_s2_domainkey" {
#   domain = digitalocean_domain.vidfast_domain.id
#   type   = "CNAME"
#   name   = "s2._domainkey"
#   value  = "s2.domainkey.u45574143.wl144.sendgrid.net."
# }
#
# resource "digitalocean_record" "dmarc" {
#   domain = digitalocean_domain.vidfast_domain.id
#   type   = "TXT"
#   name   = "_dmarc"
#   value  = "v=DMARC1; p=none;"
# }

resource "aws_route53_zone" "boostcast_domain" {
  name = "boostcast.pro"
}


resource "aws_route53_record" "email_mx_record" {
  zone_id = aws_route53_zone.boostcast_domain.zone_id
  name    = ""
  type    = "MX"
  ttl     = 300
  records = ["1 smtp.google.com."]
}


# DMARC record for email authentication and reporting
resource "aws_route53_record" "dmarc_record" {
  zone_id = aws_route53_zone.boostcast_domain.zone_id
  name    = "_dmarc"
  type    = "TXT"
  ttl     = 300
  records = ["v=DMARC1; p=quarantine; pct=50; rua=mailto:dmarc-reports@${aws_route53_zone.boostcast_domain.name}; ruf=mailto:dmarc-failures@${aws_route53_zone.boostcast_domain.name}; sp=quarantine; adkim=r; aspf=r"]
}

resource "aws_route53_record" "root_txt_records" {
  zone_id = aws_route53_zone.boostcast_domain.zone_id
  name    = ""
  type    = "TXT"
  ttl     = 300
  records = [
    "google-site-verification=7PCO3CF_k3m-TwgGTB42SjuuGXZCUJaysXvHoXStj4s",
    "v=spf1 include:_spf.google.com include:amazonses.com ~all"
  ]
}

# A record for API subdomain pointing to swarm cluster
resource "aws_route53_record" "api_a_record" {
  zone_id = aws_route53_zone.boostcast_domain.zone_id
  name    = "api"
  type    = "A"
  ttl     = 300
  records = [aws_eip.swarm_master_eip.public_ip]
}

# A record for cluster manager subdomain pointing to swarm cluster
resource "aws_route53_record" "cluster_manager_a_record" {
  zone_id = aws_route53_zone.boostcast_domain.zone_id
  name    = "cluster-manager"
  type    = "A"
  ttl     = 300
  records = [aws_eip.swarm_master_eip.public_ip]
}