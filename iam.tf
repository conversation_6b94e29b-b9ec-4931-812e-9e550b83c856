
data "aws_caller_identity" "current" {}

# ============================================================================
# IAM GROUP FOR POWER USERS
# ============================================================================

# Main IAM group for developers with PowerUserAccess permissions
# Users will be manually created through AWS Console for secure email invitations
# Note: IAM groups don't support tags in all AWS provider versions
resource "aws_iam_group" "power_users" {
  name = "boostcast-power-users"
  path = "/"
}

# ============================================================================
# BASE PERMISSIONS - AWS MANAGED POWERUSER ACCESS
# ============================================================================

# Attach AWS managed PowerUserAccess policy to the group
# PowerUserAccess provides full access to AWS services except IAM
resource "aws_iam_group_policy_attachment" "power_users_base_access" {
  group      = aws_iam_group.power_users.name
  policy_arn = "arn:aws:iam::aws:policy/PowerUserAccess"
}

# ============================================================================
# ADDITIONAL PERMISSIONS POLICY
# ============================================================================

# Custom policy for additional permissions beyond PowerUserAccess
# Provides read-only IAM visibility and self-service capabilities
resource "aws_iam_policy" "power_users_additional_permissions" {
  name        = "boostcast-power-users-additional-permissions"
  description = "Additional permissions beyond PowerUserAccess for BoostCast developers"
  path        = "/"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Sid    = "IAMReadOnlyAccess"
        Effect = "Allow"
        Action = [
          # Read-only IAM visibility for understanding current permissions
          "iam:ListUsers",
          "iam:ListRoles",
          "iam:ListPolicies",
          "iam:ListGroups",
          "iam:GetUser",
          "iam:GetRole",
          "iam:GetPolicy",
          "iam:GetGroup",
          "iam:ListAttachedUserPolicies",
          "iam:ListAttachedRolePolicies",
          "iam:ListAttachedGroupPolicies",
          "iam:ListUserPolicies",
          "iam:ListRolePolicies",
          "iam:ListGroupPolicies",
          "iam:GetUserPolicy",
          "iam:GetRolePolicy",
          "iam:GetGroupPolicy",
          "iam:ListGroupsForUser"
        ]
        Resource = "*"
      },
      {
        Sid    = "SelfServiceAccessKeyManagement"
        Effect = "Allow"
        Action = [
          # Allow users to manage their own access keys
          "iam:ListAccessKeys",
          "iam:CreateAccessKey",
          "iam:DeleteAccessKey",
          "iam:UpdateAccessKey"
        ]
        Resource = "arn:aws:iam::${data.aws_caller_identity.current.account_id}:user/$${aws:username}"
      },
      {
        Sid    = "SelfServiceMFAManagement"
        Effect = "Allow"
        Action = [
          # Allow users to manage their own MFA devices
          "iam:ListMFADevices",
          "iam:CreateVirtualMFADevice",
          "iam:EnableMFADevice",
          "iam:DeactivateMFADevice",
          "iam:ResyncMFADevice",
          "iam:DeleteVirtualMFADevice",
          "iam:ListVirtualMFADevices"
        ]
        Resource = [
          "arn:aws:iam::${data.aws_caller_identity.current.account_id}:user/$${aws:username}",
          "arn:aws:iam::${data.aws_caller_identity.current.account_id}:mfa/$${aws:username}"
        ]
      },
      {
        Sid    = "SelfServicePasswordManagement"
        Effect = "Allow"
        Action = [
          # Allow users to change their own passwords
          "iam:ChangePassword",
          "iam:GetAccountPasswordPolicy"
        ]
        Resource = "arn:aws:iam::${data.aws_caller_identity.current.account_id}:user/$${aws:username}"
      }
    ]
  })

  tags = {
    Name        = "boostcast-power-users-additional-permissions"
    Project     = "boostcast"
    Purpose     = "Additional IAM permissions for developer self-service"
    Environment = "production"
  }
}

# Attach the additional permissions policy to the power users group
resource "aws_iam_group_policy_attachment" "power_users_additional_permissions" {
  group      = aws_iam_group.power_users.name
  policy_arn = aws_iam_policy.power_users_additional_permissions.arn
}
