# AWS Amplify Configuration for BoostCast App

# Amplify App for the BoostCast React application
resource "aws_amplify_app" "boostcast_app" {
  name       = "boostcast-app"
  repository = "https://gitlab.com/boostcast/boostcast-frontend"

  # GitLab access token for repository access
  access_token = var.amplify_gitlab_token

  # Platform settings for React app
  platform = "WEB"

  # Enable auto branch creation and deployment
  enable_auto_branch_creation = false
  enable_branch_auto_build    = true
  enable_branch_auto_deletion = false

  # Custom rules for React SPA routing
  custom_rule {
    source = "/<*>"
    status = "404-200"
    target = "/index.html"
  }

  # Environment variables for the React app
  environment_variables = {
    VITE_BASE_API                = "https://${aws_route53_record.api_a_record.fqdn}"
    VITE_GOOGLE_ANALYTICS_ID     = var.app_google_analytics_id
    VITE_HOTJAR_ID               = var.app_hotjar_id
    VITE_CLARITY_ID              = var.app_clarity_id
    VITE_GOOGLE_TAG_MANAGER_ID   = var.app_google_tag_manager_id
    VITE_TURNSTILE_SITE_KEY      = var.turnstile_site_key
    VITE_STRIPE_PRICING_TABLE_ID = var.stripe_pricing_table_id
    VITE_STRIPE_PUBLISHABLE_KEY  = var.stripe_publishable_key
  }

  tags = {
    Name    = "boostcast-app"
    Project = "boostcast"
    Purpose = "React application hosting"
  }
}

# Master branch configuration
resource "aws_amplify_branch" "app_master" {
  app_id      = aws_amplify_app.boostcast_app.id
  branch_name = "master"

  # Enable automatic builds on push
  enable_auto_build = true

  # Framework detection
  framework = "React"

  # Stage for this branch
  stage = "PRODUCTION"

  tags = {
    Name    = "boostcast-app-master"
    Project = "boostcast"
    Branch  = "master"
  }
}

# Custom domain configuration for create.boostcast.pro
resource "aws_amplify_domain_association" "boostcast_app_domain" {
  app_id      = aws_amplify_app.boostcast_app.id
  domain_name = aws_route53_zone.boostcast_domain.name

  # Wait for certificate validation
  wait_for_verification = true

  # Subdomain configuration for create.boostcast.pro
  sub_domain {
    branch_name = aws_amplify_branch.app_master.branch_name
    prefix      = "create"
  }

  depends_on = [aws_amplify_branch.app_master]
}
#
