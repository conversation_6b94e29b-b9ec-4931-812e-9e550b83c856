
# Run: npx remotion lambda policies role
resource "aws_iam_policy" "remotion_lambda_policy" {
  name        = "remotion-lambda-policy"
  description = "Policy for Remotion Lambda execution role"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Sid = "0"
        Effect = "Allow"
        Action = [
          "s3:ListAllMyBuckets"
        ]
        Resource = "*"
      },
      {
        Sid = "1"
        Effect = "Allow"
        Action = [
          "s3:CreateBucket",
          "s3:ListBucket",
          "s3:PutBucketAcl",
          "s3:GetObject",
          "s3:DeleteObject",
          "s3:PutObjectAcl",
          "s3:PutObject",
          "s3:GetBucketLocation"
        ]
        Resource = [
          "arn:aws:s3:::remotionlambda-*",
          "arn:aws:s3:::remotionlambda-*/*"
        ]
      },
      {
        Sid = "2"
        Effect = "Allow"
        Action = [
          "lambda:InvokeFunction"
        ]
        Resource = [
          "arn:aws:lambda:*:*:function:remotion-render-*"
        ]
      },
      {
        Sid = "3"
        Effect = "Allow"
        Action = [
          "logs:CreateLogGroup"
        ]
        Resource = [
          "arn:aws:logs:*:*:log-group:/aws/lambda-insights"
        ]
      },
      {
        Sid = "4"
        Effect = "Allow"
        Action = [
          "logs:CreateLogStream",
          "logs:PutLogEvents"
        ]
        Resource = [
          "arn:aws:logs:*:*:log-group:/aws/lambda/remotion-render-*",
          "arn:aws:logs:*:*:log-group:/aws/lambda-insights:*"
        ]
      }
    ]
  })

  tags = {
    Name    = "remotion-lambda-policy"
    Project = "boostcast"
    Purpose = "Remotion Lambda execution"
  }
}

# Step 2: Create IAM Role for Lambda (exact name required by Remotion)
resource "aws_iam_role" "remotion_lambda_role" {
  name = "remotion-lambda-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "lambda.amazonaws.com"
        }
      }
    ]
  })

  tags = {
    Name    = "remotion-lambda-role"
    Project = "boostcast"
    Purpose = "Remotion Lambda execution"
  }
}

# Step 3: Attach the policy to the role
resource "aws_iam_role_policy_attachment" "remotion_lambda_policy_attachment" {
  role       = aws_iam_role.remotion_lambda_role.name
  policy_arn = aws_iam_policy.remotion_lambda_policy.arn
}

# Step 4: Create IAM User for CLI access
resource "aws_iam_user" "remotion_user" {
  name = "remotion-user"
  
  tags = {
    Name    = "remotion-user"
    Project = "boostcast"
    Purpose = "Remotion Lambda CLI access"
  }
}

resource "aws_iam_access_key" "remotion_user_key" {
  user = aws_iam_user.remotion_user.name
}

resource "aws_iam_user_policy" "remotion_user_policy" {
  name = "remotion-user-policy"
  user = aws_iam_user.remotion_user.name

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Sid = "0"
        Effect = "Allow"
        Action = [
          "s3:ListAllMyBuckets"
        ]
        Resource = "*"
      },
      {
        Sid = "1"
        Effect = "Allow"
        Action = [
          "s3:CreateBucket",
          "s3:ListBucket",
          "s3:PutBucketAcl",
          "s3:GetObject",
          "s3:DeleteObject",
          "s3:PutObjectAcl",
          "s3:PutObject",
          "s3:GetBucketLocation"
        ]
        Resource = [
          "arn:aws:s3:::remotionlambda-*",
          "arn:aws:s3:::remotionlambda-*/*"
        ]
      },
      {
        Sid = "2"
        Effect = "Allow"
        Action = [
          "lambda:InvokeFunction"
        ]
        Resource = [
          "arn:aws:lambda:*:*:function:remotion-render-*"
        ]
      },
      {
        Sid = "3"
        Effect = "Allow"
        Action = [
          "logs:CreateLogGroup"
        ]
        Resource = [
          "arn:aws:logs:*:*:log-group:/aws/lambda/remotion-render-*"
        ]
      },
      {
        Sid = "4"
        Effect = "Allow"
        Action = [
          "logs:CreateLogStream",
          "logs:PutLogEvents"
        ]
        Resource = [
          "arn:aws:logs:*:*:log-group:/aws/lambda/remotion-render*",
        ]
      },
      {
        Sid = "5"
        Effect = "Allow"
        Action = [
          "iam:GetRole",
          "iam:PassRole",
          "iam:ListRoles",
          "iam:SimulatePrincipalPolicy"
        ]
        Resource = "*"
      },
      {
        Sid = "6"
        Effect = "Allow"
        Action = [
          "lambda:GetFunction",
          "lambda:CreateFunction",
          "lambda:DeleteFunction",
          "lambda:UpdateFunctionCode",
          "lambda:UpdateFunctionConfiguration",
          "lambda:CreateAlias",
          "lambda:DeleteAlias",
          "lambda:GetAlias",
          "lambda:ListVersionsByFunction",
          "lambda:AddPermission",
          "lambda:RemovePermission",
          "lambda:GetPolicy",
          "lambda:ListFunctions",
          "lambda:GetLayerVersion",
          "lambda:PublishLayerVersion",
          "lambda:DeleteLayerVersion",
        ]
        Resource = "*"
      },
      {
        Sid = "7"
        Effect = "Allow"
        Action = [
          "servicequotas:GetServiceQuota"
        ]
        Resource = "*"
      },
      {
        Sid = "8"
        Effect = "Allow"
        Action = [
          "iam:SimulatePrincipalPolicy"
        ]
        Resource = "*"
      },
      {
        Sid = "9"
        Effect = "Allow"
        Action = [
          # Service Quotas
          "servicequotas:GetAWSDefaultServiceQuota",
          "servicequotas:RequestServiceQuotaIncrease",
          "servicequotas:ListRequestedServiceQuotaChangeHistoryByQuota",

          # S3
          "s3:DeleteBucket",
          "s3:PutBucketOwnershipControls",
          "s3:PutBucketPublicAccessBlock",
          "s3:PutLifecycleConfiguration",

          # Lambda
          "lambda:InvokeAsync",
          "lambda:PutFunctionEventInvokeConfig",
          "lambda:PutRuntimeManagementConfig",
          "lambda:TagResource",

          # Logs
          "logs:PutRetentionPolicy"
        ]
        Resource = "*"
      }
    ]
  })
}
